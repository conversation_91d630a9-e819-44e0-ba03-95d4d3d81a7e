@echo off
chcp 65001 >nul
echo ========================================
echo Excel转SQL工具
echo ========================================
echo.

echo 正在检查Java环境...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误：未找到Java环境，请确保已安装Java 8或更高版本
    pause
    exit /b 1
)

echo Java环境检查通过
echo.

echo 正在编译Java代码...
cd sop_service
javac -encoding UTF-8 -cp "src/main/java" -d target/classes src/main/java/com/spes/sop/tools/*.java src/main/java/com/spes/sop/common/util/CodeGenerationUtil.java
if %errorlevel% neq 0 (
    echo 编译失败，请检查代码
    pause
    exit /b 1
)

echo 编译成功
echo.

echo 正在运行Excel转SQL工具...
echo 注意：请确保已准备好Excel文件和relation.txt配置文件
echo.

java -cp target/classes com.spes.sop.tools.ExcelToSqlProcessor

echo.
echo 处理完成！请检查output目录下的SQL文件
pause
