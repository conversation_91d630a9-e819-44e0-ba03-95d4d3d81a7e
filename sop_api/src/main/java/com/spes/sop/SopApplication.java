package com.spes.sop;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * SOP应用启动类
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@SpringBootApplication(scanBasePackages = "com.spes.sop")
@EnableTransactionManagement
@EnableScheduling
@EnableConfigurationProperties
@MapperScan("com.spes.sop.*.mapper")
public class SopApplication {

    public static void main(String[] args) {
        SpringApplication.run(SopApplication.class, args);
    }
} 
