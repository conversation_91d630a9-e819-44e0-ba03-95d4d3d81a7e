package com.spes.sop.tools;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;

import java.io.File;
import java.util.Scanner;

/**
 * Excel转SQL工具主程序
 * 
 * 用于处理Excel表格数据并生成SPU和SKU的批量导入SQL语句
 *
 * <AUTHOR>
 */
@Slf4j
@SpringBootApplication(scanBasePackages = "com.spes.sop")
public class ExcelToSqlMain {

    public static void main(String[] args) {
        log.info("=== Excel转SQL工具启动 ===");
        
        // 启动Spring Boot应用上下文
        ConfigurableApplicationContext context = SpringApplication.run(ExcelToSqlMain.class, args);
        
        try {
            // 获取处理器Bean
            ExcelToSqlProcessor processor = context.getBean(ExcelToSqlProcessor.class);
            
            // 交互式处理
            processInteractively(processor);
            
        } catch (Exception e) {
            log.error("程序执行失败", e);
            System.err.println("程序执行失败：" + e.getMessage());
        } finally {
            // 关闭Spring上下文
            context.close();
            log.info("=== Excel转SQL工具结束 ===");
        }
    }

    /**
     * 交互式处理
     */
    private static void processInteractively(ExcelToSqlProcessor processor) {
        Scanner scanner = new Scanner(System.in);
        
        try {
            System.out.println("=== Excel转SQL批量导入工具 ===");
            System.out.println("功能说明：");
            System.out.println("1. 读取Excel文件中的SKU和SPU数据");
            System.out.println("2. 处理K列规格信息（数字+单位后缀）");
            System.out.println("3. 处理四级分类（使用'>'分隔符）");
            System.out.println("4. 生成批量INSERT SQL语句");
            System.out.println();

            // 获取Excel文件路径
            String excelFilePath = getExcelFilePath(scanner);
            if (excelFilePath == null) {
                return;
            }

            // 获取输出文件路径
            String outputFilePath = getOutputFilePath(scanner);

            // 检查relation.txt文件
            checkRelationFile(scanner);

            log.info("开始处理Excel文件...");
            System.out.println("正在处理Excel文件，请稍候...");

            // 执行处理
            processor.processExcelToSql(excelFilePath, outputFilePath);

            System.out.println("处理完成！");
            System.out.println("输入文件：" + excelFilePath);
            System.out.println("输出文件：" + outputFilePath);
            System.out.println();
            System.out.println("请检查生成的SQL文件，确认无误后可导入数据库。");

        } catch (Exception e) {
            log.error("处理过程中发生错误", e);
            System.err.println("处理失败：" + e.getMessage());
        } finally {
            scanner.close();
        }
    }

    /**
     * 获取Excel文件路径
     */
    private static String getExcelFilePath(Scanner scanner) {
        System.out.println("请输入Excel文件路径：");
        System.out.println("（直接回车使用默认路径：docs/财务元数据管理_SKU品类数据维护表_表格.xlsx）");
        System.out.print("Excel文件路径: ");
        
        String input = scanner.nextLine().trim();
        String excelFilePath;
        
        if (input.isEmpty()) {
            excelFilePath = "docs/财务元数据管理_SKU品类数据维护表_表格.xlsx";
        } else {
            excelFilePath = input;
        }

        // 检查文件是否存在
        File excelFile = new File(excelFilePath);
        if (!excelFile.exists()) {
            System.err.println("错误：Excel文件不存在 - " + excelFilePath);
            System.out.println("请确认文件路径是否正确。");
            
            // 提供重新输入的机会
            System.out.print("是否重新输入文件路径？(y/n): ");
            String retry = scanner.nextLine().trim();
            if ("y".equalsIgnoreCase(retry) || "yes".equalsIgnoreCase(retry)) {
                return getExcelFilePath(scanner);
            } else {
                return null;
            }
        }

        log.info("使用Excel文件：{}", excelFilePath);
        return excelFilePath;
    }

    /**
     * 获取输出文件路径
     */
    private static String getOutputFilePath(Scanner scanner) {
        System.out.println("请输入输出SQL文件路径：");
        System.out.println("（直接回车使用默认路径：output/spu_sku_insert_statements.txt）");
        System.out.print("输出文件路径: ");
        
        String input = scanner.nextLine().trim();
        String outputFilePath;
        
        if (input.isEmpty()) {
            outputFilePath = "output/spu_sku_insert_statements.txt";
        } else {
            outputFilePath = input;
        }

        log.info("使用输出文件：{}", outputFilePath);
        return outputFilePath;
    }

    /**
     * 检查relation.txt文件
     */
    private static void checkRelationFile(Scanner scanner) {
        File relationFile = new File("relation.txt");
        if (!relationFile.exists()) {
            log.warn("relation.txt文件不存在，将无法进行属性关联");
            System.out.println("警告：relation.txt文件不存在！");
            System.out.println("这意味着品牌、渠道、系列等属性将无法正确关联到ID。");
            System.out.println("生成的SQL语句中相关字段将使用NULL值。");
            System.out.println();
            System.out.print("是否继续？(y/n): ");
            
            String confirm = scanner.nextLine().trim();
            if (!"y".equalsIgnoreCase(confirm) && !"yes".equalsIgnoreCase(confirm)) {
                log.info("用户取消操作");
                System.out.println("操作已取消。请创建relation.txt文件后重新运行。");
                System.exit(0);
            }
        } else {
            log.info("找到relation.txt文件，将进行属性关联");
            System.out.println("找到relation.txt文件，将进行属性关联。");
        }
    }
}
