package com.spes.sop.tools;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Simple test for specification processing logic
 *
 * <AUTHOR>
 */
public class SpecificationTest {

    // Pattern for matching number (including decimal) + optional unit
    private static final Pattern SPEC_PATTERN = Pattern.compile("^(\\d+(?:\\.\\d+)?)\\s*[a-zA-Z]*$");

    public static void main(String[] args) {
        System.out.println("=== Excel to SQL Tool - Specification Processing Test ===");
        
        // Test specification processing logic
        testSpecificationProcessing();
        
        System.out.println("\n=== Test Complete ===");
        System.out.println("If all tests pass, the core logic is correct.");
        System.out.println("You can now prepare Excel files for complete testing.");
    }

    /**
     * Test specification processing logic
     */
    private static void testSpecificationProcessing() {
        System.out.println("\n--- Specification Processing Test ---");
        
        String[] testCases = {
            "500ml",        // should return 500
            "1000",         // should return 1000
            "250g",         // should return 250
            "1.5L",         // should return 1
            "330ML",        // should return 330
            "large",        // should return 0
            "",             // should return 0
            null,           // should return 0
            "abc123",       // should return 0
            "123abc456",    // should return 123
            "75 g",         // should return 75
            "400 ml",       // should return 400
            "special"       // should return 0
        };
        
        System.out.println("Input               -> Output");
        System.out.println("--------------------------------");
        
        for (String testCase : testCases) {
            Integer result = processSpecification(testCase);
            System.out.println(String.format("%-18s -> %d", 
                testCase == null ? "null" : "'" + testCase + "'", result));
        }
    }

    /**
     * Process specification information
     * Rules:
     * - If pure number, use it directly
     * - If number + unit suffix (like "500ml"), extract number part
     * - If starts with number (like "123abc456"), extract leading number part
     * - Other cases use 0
     */
    private static Integer processSpecification(String specificationRaw) {
        if (specificationRaw == null || specificationRaw.trim().isEmpty()) {
            return 0;
        }

        String spec = specificationRaw.trim();

        // Try to parse as pure number
        try {
            return Integer.parseInt(spec);
        } catch (NumberFormatException e) {
            // Not a pure number, try regex matching
        }

        // Use regex to match number + unit (strict: number followed by letters only)
        Matcher matcher = SPEC_PATTERN.matcher(spec);
        if (matcher.matches()) {
            try {
                // Parse number part, if decimal take integer part
                double doubleValue = Double.parseDouble(matcher.group(1));
                return (int) doubleValue;
            } catch (NumberFormatException e) {
                System.out.println("Warning: Cannot parse number part: " + spec);
            }
        }

        // Try to extract leading number part (handle cases like "123abc456")
        Pattern leadingNumberPattern = Pattern.compile("^(\\d+(?:\\.\\d+)?)");
        Matcher leadingMatcher = leadingNumberPattern.matcher(spec);
        if (leadingMatcher.find()) {
            try {
                double doubleValue = Double.parseDouble(leadingMatcher.group(1));
                System.out.println("Extracted leading number from: " + spec + " -> " + (int) doubleValue);
                return (int) doubleValue;
            } catch (NumberFormatException e) {
                System.out.println("Warning: Cannot parse leading number part: " + spec);
            }
        }

        System.out.println("Warning: Specification format not supported, using default 0: " + spec);
        return 0;
    }
}
