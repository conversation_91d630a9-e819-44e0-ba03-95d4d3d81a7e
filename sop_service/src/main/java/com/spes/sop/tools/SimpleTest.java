package com.spes.sop.tools;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 简单测试类，用于验证规格处理逻辑
 *
 * <AUTHOR>
 */
public class SimpleTest {

    // 规格处理的正则表达式：匹配数字+可选单位
    private static final Pattern SPEC_PATTERN = Pattern.compile("^(\\d+)\\s*[a-zA-Z]*$");

    public static void main(String[] args) {
        System.out.println("=== Excel转SQL工具 - 规格处理测试 ===");
        
        // 测试规格处理逻辑
        testSpecificationProcessing();
        
        System.out.println("\n=== 测试完成 ===");
        System.out.println("如果所有测试都通过，说明核心逻辑正确。");
        System.out.println("接下来可以准备Excel文件进行完整测试。");
    }

    /**
     * 测试规格处理逻辑
     */
    private static void testSpecificationProcessing() {
        System.out.println("\n--- 规格处理测试 ---");
        
        String[] testCases = {
            "500ml",        // 应该返回 500
            "1000",         // 应该返回 1000
            "250g",         // 应该返回 250
            "1.5L",         // 应该返回 1
            "330ML",        // 应该返回 330
            "大号",          // 应该返回 0
            "",             // 应该返回 0
            null,           // 应该返回 0
            "abc123",       // 应该返回 0
            "123abc456",    // 应该返回 123
            "75 g",         // 应该返回 75
            "400 ml",       // 应该返回 400
            "特殊规格"       // 应该返回 0
        };
        
        System.out.println("输入格式          -> 输出结果");
        System.out.println("--------------------------------");
        
        for (String testCase : testCases) {
            Integer result = processSpecification(testCase);
            System.out.println(String.format("%-15s -> %d", 
                testCase == null ? "null" : "'" + testCase + "'", result));
        }
    }

    /**
     * 处理规格信息
     * 规则：
     * - 如果是纯数字，直接使用该数字
     * - 如果是数字+单位后缀（如"500ml"），去除后缀后使用数字部分
     * - 其他情况统一使用0
     */
    private static Integer processSpecification(String specificationRaw) {
        if (specificationRaw == null || specificationRaw.trim().isEmpty()) {
            return 0;
        }

        String spec = specificationRaw.trim();

        // 尝试直接解析为数字
        try {
            return Integer.parseInt(spec);
        } catch (NumberFormatException e) {
            // 不是纯数字，尝试正则匹配
        }

        // 使用正则表达式匹配数字+单位
        Matcher matcher = SPEC_PATTERN.matcher(spec);
        if (matcher.matches()) {
            try {
                return Integer.parseInt(matcher.group(1));
            } catch (NumberFormatException e) {
                System.out.println("警告：无法解析规格数字部分：" + spec);
            }
        }

        System.out.println("警告：规格信息格式不符合要求，使用默认值0：" + spec);
        return 0;
    }
}
