package com.spes.sop.tools;

import com.spes.sop.common.util.CodeGenerationUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Component;

import java.io.*;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;

/**
 * Excel转SQL工具类
 * 用于读取Excel文件并生成SKU和SPU的INSERT语句
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Slf4j
@Component
public class ExcelToSqlUtil {

    private static final String RELATION_FILE_PATH = "relation.txt";
    private static final String OUTPUT_SQL_FILE_PATH = "insert_statements.txt";
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * 属性关联关系映射
     */
    private Map<String, String> relationMap = new HashMap<>();

    /**
     * 处理Excel文件并生成SQL语句
     *
     * @param excelFilePath Excel文件路径
     * @throws Exception 处理异常
     */
    public void processExcelToSql(String excelFilePath) throws Exception {
        log.info("开始处理Excel文件：{}", excelFilePath);

        // 1. 加载属性关联关系
        loadRelationMapping();

        // 2. 读取Excel文件
        List<ExcelRowData> excelData = readExcelFile(excelFilePath);

        // 3. 生成SQL语句
        generateSqlStatements(excelData);

        log.info("Excel文件处理完成，SQL语句已生成到：{}", OUTPUT_SQL_FILE_PATH);
    }

    /**
     * 加载属性关联关系映射
     */
    private void loadRelationMapping() throws IOException {
        log.info("开始加载属性关联关系映射...");
        
        try (BufferedReader reader = Files.newBufferedReader(Paths.get(RELATION_FILE_PATH))) {
            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (line.isEmpty() || line.startsWith("#")) {
                    continue;
                }
                
                String[] parts = line.split("=");
                if (parts.length == 2) {
                    relationMap.put(parts[0].trim(), parts[1].trim());
                }
            }
        }
        
        log.info("属性关联关系映射加载完成，共{}条记录", relationMap.size());
    }

    /**
     * 读取Excel文件
     */
    private List<ExcelRowData> readExcelFile(String excelFilePath) throws IOException {
        List<ExcelRowData> excelData = new ArrayList<>();
        
        try (FileInputStream fis = new FileInputStream(excelFilePath);
             XSSFWorkbook workbook = new XSSFWorkbook(fis)) {
            
            Sheet sheet = workbook.getSheetAt(0);
            Iterator<Row> rowIterator = sheet.iterator();
            
            // 跳过表头
            if (rowIterator.hasNext()) {
                rowIterator.next();
            }
            
            while (rowIterator.hasNext()) {
                Row row = rowIterator.next();
                ExcelRowData rowData = parseRowData(row);
                if (rowData != null) {
                    excelData.add(rowData);
                }
            }
        }
        
        log.info("Excel文件读取完成，共{}条数据", excelData.size());
        return excelData;
    }

    /**
     * 解析行数据
     */
    private ExcelRowData parseRowData(Row row) {
        if (row == null) {
            return null;
        }

        ExcelRowData rowData = new ExcelRowData();
        
        // A列：SKU编码
        rowData.setSkuCode(getCellStringValue(row.getCell(0)));
        
        // B列：SKU名称
        rowData.setSkuName(getCellStringValue(row.getCell(1)));
        
        // C列：条码
        rowData.setBarCode(getCellStringValue(row.getCell(2)));
        
        // D列：备案号
        rowData.setFilingNumber(getCellStringValue(row.getCell(3)));
        
        // E列：SPU名称
        rowData.setSpuName(getCellStringValue(row.getCell(4)));
        
        // F列：描述
        rowData.setDescription(getCellStringValue(row.getCell(5)));
        
        // G列：品牌
        rowData.setBrand(getCellStringValue(row.getCell(6)));
        
        // H列：四级分类
        rowData.setClassification(getCellStringValue(row.getCell(7)));
        
        // I列：渠道
        rowData.setChannel(getCellStringValue(row.getCell(8)));
        
        // J列：系列
        rowData.setSeries(getCellStringValue(row.getCell(9)));
        
        // K列：规格（需要特殊处理）
        rowData.setSpecification(processSpecification(getCellStringValue(row.getCell(10))));
        
        // L列：分类
        rowData.setCategory(getCellStringValue(row.getCell(11)));
        
        // M列：公允价值
        rowData.setFairPrice(getCellBigDecimalValue(row.getCell(12)));
        
        // N列：底价
        rowData.setBasePrice(getCellBigDecimalValue(row.getCell(13)));
        
        // O列：成本
        rowData.setCost(getCellBigDecimalValue(row.getCell(14)));
        
        // P列：是否赠品
        rowData.setGift(getCellBooleanValue(row.getCell(15)));
        
        // Q列：是否包裹卡
        rowData.setCard(getCellBooleanValue(row.getCell(16)));

        return rowData;
    }

    /**
     * 处理规格字段
     * 如果直接是数字或者去除后缀（ml）是数字就使用数字，其余使用0
     */
    private Integer processSpecification(String specification) {
        if (specification == null || specification.trim().isEmpty()) {
            return 0;
        }
        
        String spec = specification.trim();
        
        // 直接是数字
        if (isNumeric(spec)) {
            try {
                return Integer.parseInt(spec);
            } catch (NumberFormatException e) {
                return 0;
            }
        }
        
        // 去除ml后缀
        if (spec.toLowerCase().endsWith("ml")) {
            String numericPart = spec.substring(0, spec.length() - 2).trim();
            if (isNumeric(numericPart)) {
                try {
                    return Integer.parseInt(numericPart);
                } catch (NumberFormatException e) {
                    return 0;
                }
            }
        }
        
        return 0;
    }

    /**
     * 判断字符串是否为数字
     */
    private boolean isNumeric(String str) {
        if (str == null || str.trim().isEmpty()) {
            return false;
        }
        return Pattern.matches("\\d+", str.trim());
    }

    /**
     * 获取单元格字符串值
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return DATE_FORMAT.format(cell.getDateCellValue());
                } else {
                    return String.valueOf((long) cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    /**
     * 获取单元格BigDecimal值
     */
    private BigDecimal getCellBigDecimalValue(Cell cell) {
        if (cell == null) {
            return BigDecimal.ZERO;
        }
        
        switch (cell.getCellType()) {
            case NUMERIC:
                return BigDecimal.valueOf(cell.getNumericCellValue());
            case STRING:
                try {
                    return new BigDecimal(cell.getStringCellValue().trim());
                } catch (NumberFormatException e) {
                    return BigDecimal.ZERO;
                }
            default:
                return BigDecimal.ZERO;
        }
    }

    /**
     * 获取单元格布尔值
     */
    private Boolean getCellBooleanValue(Cell cell) {
        if (cell == null) {
            return false;
        }
        
        switch (cell.getCellType()) {
            case BOOLEAN:
                return cell.getBooleanCellValue();
            case STRING:
                String value = cell.getStringCellValue().trim().toLowerCase();
                return "true".equals(value) || "1".equals(value) || "是".equals(value);
            case NUMERIC:
                return cell.getNumericCellValue() == 1;
            default:
                return false;
        }
    }

    /**
     * 生成SQL语句
     */
    private void generateSqlStatements(List<ExcelRowData> excelData) throws IOException {
        log.info("开始生成SQL语句...");
        
        try (BufferedWriter writer = Files.newBufferedWriter(Paths.get(OUTPUT_SQL_FILE_PATH))) {
            writer.write("-- SKU和SPU批量导入SQL语句\n");
            writer.write("-- 生成时间：" + DATE_FORMAT.format(new Date()) + "\n\n");
            
            Map<String, Long> spuCodeToIdMap = new HashMap<>();
            long spuIdCounter = 1;
            long skuIdCounter = 1;
            
            for (ExcelRowData rowData : excelData) {
                // 生成SPU SQL（如果SPU不存在）
                String spuKey = generateSpuKey(rowData);
                Long spuId = spuCodeToIdMap.get(spuKey);
                
                if (spuId == null) {
                    spuId = spuIdCounter++;
                    spuCodeToIdMap.put(spuKey, spuId);
                    
                    String spuSql = generateSpuInsertSql(rowData, spuId);
                    writer.write(spuSql + "\n");
                }
                
                // 生成SKU SQL
                String skuSql = generateSkuInsertSql(rowData, spuId, skuIdCounter++);
                writer.write(skuSql + "\n");
            }
        }
        
        log.info("SQL语句生成完成");
    }

    /**
     * 生成SPU键值，用于判断SPU是否重复
     */
    private String generateSpuKey(ExcelRowData rowData) {
        return rowData.getSpuName() + "_" + rowData.getBrand() + "_" + rowData.getClassification();
    }

    /**
     * 生成SPU INSERT语句
     */
    private String generateSpuInsertSql(ExcelRowData rowData, Long spuId) {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO spu (");
        sql.append("id, spu_code, spu_name, description, brand_id, ");
        sql.append("first_classification, second_classification, third_classification, fourth_classification, ");
        sql.append("channel_id, series_id, category_id, creator_id, updater_id, create_time, update_time, deleted");
        sql.append(") VALUES (");
        
        // 基本信息
        sql.append(spuId).append(", ");
        sql.append("'").append(CodeGenerationUtil.generateSpuCode()).append("', ");
        sql.append("'").append(escapeString(rowData.getSpuName())).append("', ");
        sql.append("'").append(escapeString(rowData.getDescription())).append("', ");
        
        // 品牌ID
        sql.append(getAttributeId(rowData.getBrand())).append(", ");
        
        // 四级分类
        String[] classifications = parseClassifications(rowData.getClassification());
        sql.append(getClassificationId(classifications, 0)).append(", ");
        sql.append(getClassificationId(classifications, 1)).append(", ");
        sql.append(getClassificationId(classifications, 2)).append(", ");
        sql.append(getClassificationId(classifications, 3)).append(", ");
        
        // 其他属性
        sql.append(getAttributeId(rowData.getChannel())).append(", ");
        sql.append(getAttributeId(rowData.getSeries())).append(", ");
        sql.append(getAttributeId(rowData.getCategory())).append(", ");
        
        // 系统字段
        sql.append("1, 1, ");
        sql.append("'").append(DATE_FORMAT.format(new Date())).append("', ");
        sql.append("'").append(DATE_FORMAT.format(new Date())).append("', ");
        sql.append("0");
        
        sql.append(");");
        
        return sql.toString();
    }

    /**
     * 生成SKU INSERT语句
     */
    private String generateSkuInsertSql(ExcelRowData rowData, Long spuId, Long skuId) {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO sku (");
        sql.append("id, spu_id, sku_code, sku_name, bar_code, filing_number, ");
        sql.append("status, specification, gift, card, ");
        sql.append("fair_price, base_price, cost, ");
        sql.append("creator_id, updater_id, create_time, update_time, deleted");
        sql.append(") VALUES (");
        
        // 基本信息
        sql.append(skuId).append(", ");
        sql.append(spuId).append(", ");
        sql.append("'").append(escapeString(rowData.getSkuCode())).append("', ");
        sql.append("'").append(escapeString(rowData.getSkuName())).append("', ");
        sql.append("'").append(escapeString(rowData.getBarCode())).append("', ");
        sql.append("'").append(escapeString(rowData.getFilingNumber())).append("', ");
        
        // 状态和规格
        sql.append("'DRAFT', ");
        sql.append(rowData.getSpecification()).append(", ");
        sql.append(rowData.getGift() ? 1 : 0).append(", ");
        sql.append(rowData.getCard() ? 1 : 0).append(", ");
        
        // 价格信息
        sql.append(rowData.getFairPrice()).append(", ");
        sql.append(rowData.getBasePrice()).append(", ");
        sql.append(rowData.getCost()).append(", ");
        
        // 系统字段
        sql.append("1, 1, ");
        sql.append("'").append(DATE_FORMAT.format(new Date())).append("', ");
        sql.append("'").append(DATE_FORMAT.format(new Date())).append("', ");
        sql.append("0");
        
        sql.append(");");
        
        return sql.toString();
    }

    /**
     * 解析四级分类
     */
    private String[] parseClassifications(String classification) {
        if (classification == null || classification.trim().isEmpty()) {
            return new String[]{"", "", "", ""};
        }
        
        String[] parts = classification.split(">");
        String[] result = new String[4];
        
        for (int i = 0; i < 4; i++) {
            if (i < parts.length) {
                result[i] = parts[i].trim();
            } else {
                result[i] = "";
            }
        }
        
        return result;
    }

    /**
     * 获取属性ID
     */
    private Long getAttributeId(String attributeName) {
        if (attributeName == null || attributeName.trim().isEmpty()) {
            return 1L;
        }
        
        String id = relationMap.get(attributeName.trim());
        if (id != null) {
            try {
                return Long.parseLong(id);
            } catch (NumberFormatException e) {
                log.warn("无法解析属性ID：{} -> {}", attributeName, id);
            }
        }
        
        return 1L;
    }

    /**
     * 获取分类ID
     */
    private Long getClassificationId(String[] classifications, int index) {
        if (index >= classifications.length || classifications[index].isEmpty()) {
            return 1L;
        }
        
        // 构建完整的分类路径
        StringBuilder fullPath = new StringBuilder();
        for (int i = 0; i <= index; i++) {
            if (i > 0) {
                fullPath.append(">");
            }
            fullPath.append(classifications[i]);
        }
        
        String fullClassification = fullPath.toString();
        for (Map.Entry<String, String> entry : relationMap.entrySet()) {
            if (entry.getKey().startsWith(fullClassification)) {
                String[] ids = entry.getValue().split(",");
                if (index < ids.length) {
                    try {
                        return Long.parseLong(ids[index].trim());
                    } catch (NumberFormatException e) {
                        log.warn("无法解析分类ID：{} -> {}", fullClassification, ids[index]);
                    }
                }
            }
        }
        
        return 1L;
    }

    /**
     * 转义字符串
     */
    private String escapeString(String str) {
        if (str == null) {
            return "";
        }
        return str.replace("'", "''").replace("\\", "\\\\");
    }

    /**
     * Excel行数据对象
     */
    private static class ExcelRowData {
        private String skuCode;
        private String skuName;
        private String barCode;
        private String filingNumber;
        private String spuName;
        private String description;
        private String brand;
        private String classification;
        private String channel;
        private String series;
        private Integer specification;
        private String category;
        private BigDecimal fairPrice;
        private BigDecimal basePrice;
        private BigDecimal cost;
        private Boolean gift;
        private Boolean card;

        // Getters and Setters
        public String getSkuCode() { return skuCode; }
        public void setSkuCode(String skuCode) { this.skuCode = skuCode; }

        public String getSkuName() { return skuName; }
        public void setSkuName(String skuName) { this.skuName = skuName; }

        public String getBarCode() { return barCode; }
        public void setBarCode(String barCode) { this.barCode = barCode; }

        public String getFilingNumber() { return filingNumber; }
        public void setFilingNumber(String filingNumber) { this.filingNumber = filingNumber; }

        public String getSpuName() { return spuName; }
        public void setSpuName(String spuName) { this.spuName = spuName; }

        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }

        public String getBrand() { return brand; }
        public void setBrand(String brand) { this.brand = brand; }

        public String getClassification() { return classification; }
        public void setClassification(String classification) { this.classification = classification; }

        public String getChannel() { return channel; }
        public void setChannel(String channel) { this.channel = channel; }

        public String getSeries() { return series; }
        public void setSeries(String series) { this.series = series; }

        public Integer getSpecification() { return specification; }
        public void setSpecification(Integer specification) { this.specification = specification; }

        public String getCategory() { return category; }
        public void setCategory(String category) { this.category = category; }

        public BigDecimal getFairPrice() { return fairPrice; }
        public void setFairPrice(BigDecimal fairPrice) { this.fairPrice = fairPrice; }

        public BigDecimal getBasePrice() { return basePrice; }
        public void setBasePrice(BigDecimal basePrice) { this.basePrice = basePrice; }

        public BigDecimal getCost() { return cost; }
        public void setCost(BigDecimal cost) { this.cost = cost; }

        public Boolean getGift() { return gift; }
        public void setGift(Boolean gift) { this.gift = gift; }

        public Boolean getCard() { return card; }
        public void setCard(Boolean card) { this.card = card; }
    }
} 