package com.spes.sop.tools;

import com.spes.sop.common.util.CodeGenerationUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.*;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Excel表格数据处理器，用于生成SPU和SKU的批量导入SQL语句
 *
 * 处理逻辑：
 * - A列：SKU编码（直接使用）
 * - E-M列：SPU信息（排除K列）
 * - K列：规格信息（特殊处理：数字+单位后缀）
 * - 四级分类：使用'>'分隔符拆分
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ExcelToSqlProcessor {

    private static final String DEFAULT_EXCEL_FILE_PATH = "docs/财务元数据管理_SKU品类数据维护表_表格.xlsx";
    private static final String RELATION_FILE_PATH = "relation.txt";
    private static final String DEFAULT_OUTPUT_SQL_FILE_PATH = "output/spu_sku_insert_statements.txt";
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    // 属性关联映射
    private final Map<String, Long> brandMapping = new HashMap<>();
    private final Map<String, Long> categoryMapping = new HashMap<>();
    private final Map<String, Long> channelMapping = new HashMap<>();
    private final Map<String, Long> seriesMapping = new HashMap<>();
    private final Map<String, String> classificationMapping = new HashMap<>();

    // 规格处理的正则表达式：匹配数字（包括小数）+可选单位
    private static final Pattern SPEC_PATTERN = Pattern.compile("^(\\d+(?:\\.\\d+)?)\\s*[a-zA-Z]*$");

    /**
     * 处理Excel文件并生成SQL语句
     *
     * @param excelFilePath Excel文件路径
     * @param outputFilePath 输出文件路径
     */
    public void processExcelToSql(String excelFilePath, String outputFilePath) {
        try {
            log.info("=== 开始处理Excel文件 ===");
            log.info("输入文件：{}", excelFilePath);
            log.info("输出文件：{}", outputFilePath);

            // 加载属性关联关系
            loadRelationMapping();

            // 读取Excel文件
            List<ExcelRowData> dataList = readExcelFile(excelFilePath);

            // 生成SQL语句
            generateSqlStatements(dataList, outputFilePath);

            log.info("=== Excel处理完成 ===");

        } catch (Exception e) {
            log.error("处理Excel文件时发生错误", e);
            throw new RuntimeException("处理Excel文件失败：" + e.getMessage(), e);
        }
    }

    /**
     * 加载属性关联关系
     */
    private void loadRelationMapping() throws IOException {
        log.info("开始加载属性关联关系：{}", RELATION_FILE_PATH);

        File relationFile = new File(RELATION_FILE_PATH);
        if (!relationFile.exists()) {
            log.warn("relation.txt文件不存在，将使用默认值");
            return;
        }

        try (BufferedReader reader = Files.newBufferedReader(Paths.get(RELATION_FILE_PATH))) {
            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (line.isEmpty() || line.startsWith("--") || line.startsWith("#")) {
                    continue;
                }

                // 解析关联关系：属性名称=属性ID
                String[] parts = line.split("=");
                if (parts.length == 2) {
                    String name = parts[0].trim();
                    String value = parts[1].trim();

                    // 根据名称类型分类存储
                    if (name.contains("品牌")) {
                        brandMapping.put(name, Long.parseLong(value));
                    } else if (name.contains("渠道")) {
                        channelMapping.put(name, Long.parseLong(value));
                    } else if (name.contains("系列")) {
                        seriesMapping.put(name, Long.parseLong(value));
                    } else if (name.contains("分类") && !name.contains(">")) {
                        categoryMapping.put(name, Long.parseLong(value));
                    } else if (name.contains(">")) {
                        // 四级分类处理
                        classificationMapping.put(name, value);
                    }
                }
            }
        }

        log.info("属性关联关系加载完成 - 品牌:{}, 分类:{}, 渠道:{}, 系列:{}, 四级分类:{}",
                brandMapping.size(), categoryMapping.size(), channelMapping.size(),
                seriesMapping.size(), classificationMapping.size());
    }

    /**
     * 读取Excel文件
     */
    private List<ExcelRowData> readExcelFile(String excelFilePath) throws IOException {
        List<ExcelRowData> dataList = new ArrayList<>();

        try (FileInputStream fis = new FileInputStream(excelFilePath);
             XSSFWorkbook workbook = new XSSFWorkbook(fis)) {

            Sheet sheet = workbook.getSheetAt(0);
            log.info("Excel文件共有 {} 行数据", sheet.getLastRowNum() + 1);

            // 跳过标题行，从第二行开始读取
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) {
                    continue;
                }

                ExcelRowData rowData = parseRowData(row);
                if (rowData != null && isValidRowData(rowData)) {
                    dataList.add(rowData);
                }
            }
        }

        log.info("成功读取 {} 条有效数据", dataList.size());
        return dataList;
    }

    /**
     * 解析行数据
     */
    private ExcelRowData parseRowData(Row row) {
        try {
            ExcelRowData data = new ExcelRowData();

            // A列：SKU编码
            data.setSkuCode(getCellStringValue(row.getCell(0)));

            // B-D列：SKU基本信息
            data.setSkuName(getCellStringValue(row.getCell(1)));
            data.setBarCode(getCellStringValue(row.getCell(2)));
            data.setFilingNumber(getCellStringValue(row.getCell(3)));

            // E-M列：SPU信息（排除K列）
            data.setSpuName(getCellStringValue(row.getCell(4))); // E列
            data.setDescription(getCellStringValue(row.getCell(5))); // F列
            data.setBrand(getCellStringValue(row.getCell(6))); // G列
            data.setClassification(getCellStringValue(row.getCell(7))); // H列
            data.setChannel(getCellStringValue(row.getCell(8))); // I列
            data.setSeries(getCellStringValue(row.getCell(9))); // J列
            // K列是规格信息，单独处理
            data.setSpecificationRaw(getCellStringValue(row.getCell(10))); // K列
            data.setCategory(getCellStringValue(row.getCell(11))); // L列
            // M列等其他信息
            data.setFairPrice(getCellBigDecimalValue(row.getCell(12))); // M列

            // 处理规格信息
            data.setSpecification(processSpecification(data.getSpecificationRaw()));

            // 处理四级分类
            processClassification(data);

            // 处理价格信息（如果有更多列）
            if (row.getLastCellNum() > 13) {
                data.setBasePrice(getCellBigDecimalValue(row.getCell(13))); // N列
            }
            if (row.getLastCellNum() > 14) {
                data.setCost(getCellBigDecimalValue(row.getCell(14))); // O列
            }

            return data;

        } catch (Exception e) {
            log.warn("解析第{}行数据时发生错误：{}", row.getRowNum() + 1, e.getMessage());
            return null;
        }
    }

    /**
     * 处理规格信息
     * 规则：
     * - 如果是纯数字，直接使用该数字
     * - 如果是数字+单位后缀（如"500ml"），去除后缀后使用数字部分
     * - 其他情况统一使用0
     */
    private Integer processSpecification(String specificationRaw) {
        if (!StringUtils.hasText(specificationRaw)) {
            return 0;
        }

        String spec = specificationRaw.trim();

        // 尝试直接解析为数字
        try {
            return Integer.parseInt(spec);
        } catch (NumberFormatException e) {
            // 不是纯数字，尝试正则匹配
        }

        // 使用正则表达式匹配数字+单位
        Matcher matcher = SPEC_PATTERN.matcher(spec);
        if (matcher.matches()) {
            try {
                return Integer.parseInt(matcher.group(1));
            } catch (NumberFormatException e) {
                log.warn("无法解析规格数字部分：{}", spec);
            }
        }

        log.warn("规格信息格式不符合要求，使用默认值0：{}", spec);
        return 0;
    }

    /**
     * 处理四级分类
     */
    private void processClassification(ExcelRowData data) {
        String classification = data.getClassification();
        if (!StringUtils.hasText(classification)) {
            return;
        }

        // 查找匹配的四级分类配置
        String matchedConfig = classificationMapping.get(classification);
        if (matchedConfig != null) {
            // 解析四级分类ID：格式为 "1,2,3,4"
            String[] ids = matchedConfig.split(",");
            if (ids.length >= 4) {
                try {
                    data.setFirstClassification(Long.parseLong(ids[0].trim()));
                    data.setSecondClassification(Long.parseLong(ids[1].trim()));
                    data.setThirdClassification(Long.parseLong(ids[2].trim()));
                    data.setFourthClassification(Long.parseLong(ids[3].trim()));
                } catch (NumberFormatException e) {
                    log.warn("解析四级分类ID失败：{}", matchedConfig);
                }
            }
        } else {
            // 尝试按'>'分隔符拆分
            String[] parts = classification.split(">");
            if (parts.length >= 4) {
                // 这里可以根据实际需求进一步处理每个分类级别
                log.info("检测到四级分类：{}", Arrays.toString(parts));
            }
        }
    }

    /**
     * 验证行数据是否有效
     */
    private boolean isValidRowData(ExcelRowData data) {
        // SKU编码和SPU名称是必需的
        return StringUtils.hasText(data.getSkuCode()) && StringUtils.hasText(data.getSpuName());
    }

    /**
     * 生成SQL语句
     */
    private void generateSqlStatements(List<ExcelRowData> dataList, String outputFilePath) throws IOException {
        // 确保输出目录存在
        File outputFile = new File(outputFilePath);
        File parentDir = outputFile.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            parentDir.mkdirs();
        }

        try (BufferedWriter writer = Files.newBufferedWriter(Paths.get(outputFilePath))) {
            String currentTime = DATE_FORMAT.format(new Date());

            writer.write("-- SPU和SKU批量导入SQL语句\n");
            writer.write("-- 生成时间：" + currentTime + "\n");
            writer.write("-- 数据条数：" + dataList.size() + "\n\n");

            // 用于SPU去重
            Set<String> spuKeys = new HashSet<>();
            Map<String, String> spuCodeMap = new HashMap<>();
            List<String> spuInserts = new ArrayList<>();
            List<String> skuInserts = new ArrayList<>();

            for (ExcelRowData data : dataList) {
                // 生成SPU唯一键
                String spuKey = generateSpuKey(data);
                String spuCode;

                // 检查SPU是否已存在
                if (!spuKeys.contains(spuKey)) {
                    // 新的SPU
                    spuCode = CodeGenerationUtil.generateSpuCode();
                    spuKeys.add(spuKey);
                    spuCodeMap.put(spuKey, spuCode);

                    String spuInsert = generateSpuInsert(data, spuCode, currentTime);
                    spuInserts.add(spuInsert);
                } else {
                    // 已存在的SPU
                    spuCode = spuCodeMap.get(spuKey);
                }

                // 生成SKU INSERT语句
                String skuInsert = generateSkuInsert(data, spuCode, currentTime);
                skuInserts.add(skuInsert);
            }

            // 写入SPU INSERT语句
            writer.write("-- SPU数据插入语句（共" + spuInserts.size() + "条）\n");
            for (String spuInsert : spuInserts) {
                writer.write(spuInsert + "\n");
            }

            writer.write("\n-- SKU数据插入语句（共" + skuInserts.size() + "条）\n");
            // 写入SKU INSERT语句
            for (String skuInsert : skuInserts) {
                writer.write(skuInsert + "\n");
            }

            log.info("SQL语句生成完成 - SPU:{}, SKU:{}", spuInserts.size(), skuInserts.size());
        }
    }

    /**
     * 生成SPU唯一键（用于去重）
     */
    private String generateSpuKey(ExcelRowData data) {
        return String.format("%s_%s_%s_%s_%s_%s_%s",
                Objects.toString(data.getSpuName(), ""),
                Objects.toString(data.getDescription(), ""),
                Objects.toString(data.getBrand(), ""),
                Objects.toString(data.getFirstClassification(), ""),
                Objects.toString(data.getSecondClassification(), ""),
                Objects.toString(data.getThirdClassification(), ""),
                Objects.toString(data.getChannel(), ""));
    }

    /**
     * 生成SPU INSERT语句
     */
    private String generateSpuInsert(ExcelRowData data, String spuCode, String currentTime) {
        // 获取属性ID
        Long brandId = brandMapping.get(data.getBrand());
        Long channelId = channelMapping.get(data.getChannel());
        Long categoryId = categoryMapping.get(data.getCategory());

        return String.format(
            "INSERT INTO spu (spu_code, spu_name, description, brand_id, first_classification, " +
            "second_classification, third_classification, fourth_classification, channel_id, category_id, " +
            "creator_id, updater_id, create_time, update_time, deleted) VALUES " +
            "('%s', '%s', '%s', %s, %s, %s, %s, %s, %s, %s, 1, 1, '%s', '%s', 0);",
            spuCode,
            escapeString(data.getSpuName()),
            escapeString(data.getDescription()),
            brandId != null ? brandId : "NULL",
            data.getFirstClassification() != null ? data.getFirstClassification() : "NULL",
            data.getSecondClassification() != null ? data.getSecondClassification() : "NULL",
            data.getThirdClassification() != null ? data.getThirdClassification() : "NULL",
            data.getFourthClassification() != null ? data.getFourthClassification() : "NULL",
            channelId != null ? channelId : "NULL",
            categoryId != null ? categoryId : "NULL",
            currentTime,
            currentTime
        );
    }

    /**
     * 生成SKU INSERT语句
     */
    private String generateSkuInsert(ExcelRowData data, String spuCode, String currentTime) {
        return String.format(
            "INSERT INTO sku (spu_id, sku_code, sku_name, bar_code, filing_number, " +
            "status, specification, gift, card, fair_price, base_price, cost, " +
            "create_time, update_time, creator_id, updater_id, deleted) VALUES " +
            "((SELECT id FROM spu WHERE spu_code = '%s'), '%s', '%s', '%s', '%s', " +
            "'ACTIVE', %s, 0, 0, %s, %s, %s, '%s', '%s', 1, 1, 0);",
            spuCode,
            escapeString(data.getSkuCode()),
            escapeString(data.getSkuName()),
            escapeString(data.getBarCode()),
            escapeString(data.getFilingNumber()),
            data.getSpecification(),
            data.getFairPrice() != null ? data.getFairPrice() : "0.00",
            data.getBasePrice() != null ? data.getBasePrice() : "0.00",
            data.getCost() != null ? data.getCost() : "0.00",
            currentTime,
            currentTime
        );
    }

    /**
     * 转义SQL字符串
     */
    private String escapeString(String str) {
        if (str == null) {
            return "";
        }
        return str.replace("'", "''").replace("\\", "\\\\");
    }

    /**
     * 获取单元格字符串值
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return DATE_FORMAT.format(cell.getDateCellValue());
                } else {
                    // 处理数字，避免科学计数法
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == (long) numericValue) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                try {
                    return cell.getStringCellValue().trim();
                } catch (Exception e) {
                    return String.valueOf(cell.getNumericCellValue());
                }
            default:
                return "";
        }
    }

    /**
     * 获取单元格BigDecimal值
     */
    private BigDecimal getCellBigDecimalValue(Cell cell) {
        if (cell == null) {
            return BigDecimal.ZERO;
        }

        try {
            switch (cell.getCellType()) {
                case NUMERIC:
                    return BigDecimal.valueOf(cell.getNumericCellValue());
                case STRING:
                    String strValue = cell.getStringCellValue().trim();
                    if (StringUtils.hasText(strValue)) {
                        return new BigDecimal(strValue);
                    }
                    return BigDecimal.ZERO;
                case FORMULA:
                    return BigDecimal.valueOf(cell.getNumericCellValue());
                default:
                    return BigDecimal.ZERO;
            }
        } catch (Exception e) {
            log.warn("解析单元格数值失败：{}", e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    /**
     * Excel行数据模型
     */
    @Data
    public static class ExcelRowData {
        // SKU信息
        private String skuCode;
        private String skuName;
        private String barCode;
        private String filingNumber;
        private Integer specification;
        private String specificationRaw; // 原始规格信息
        private BigDecimal fairPrice;
        private BigDecimal basePrice;
        private BigDecimal cost;

        // SPU信息
        private String spuName;
        private String description;
        private String brand;
        private String classification;
        private String channel;
        private String series;
        private String category;

        // 四级分类
        private Long firstClassification;
        private Long secondClassification;
        private Long thirdClassification;
        private Long fourthClassification;
    }
}
