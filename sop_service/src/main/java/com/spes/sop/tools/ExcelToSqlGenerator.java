package com.spes.sop.tools;

import com.spes.sop.common.util.ExcelDataProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

import javax.annotation.Resource;
import java.io.File;
import java.util.Scanner;

/**
 * Excel转SQL生成器主程序
 * 用于将Excel文件转换为SKU和SPU的INSERT语句
 *
 * <AUTHOR>
 * @since 2024-12-28
 */
@ComponentScan(basePackages = "com.spes.sop")
@Slf4j
public class ExcelToSqlGenerator implements CommandLineRunner {

    @Resource
    private ExcelDataProcessor excelDataProcessor;
    @Override
    public void run(String... args) throws Exception {
        log.info("=== Excel转SQL生成器启动 ===");
        
        Scanner scanner = new Scanner(System.in);
        
        try {
            // 获取Excel文件路径
            System.out.print("请输入Excel文件路径（默认：docs/财务元数据管理_SKU品类数据维护表_表格.xlsx）: ");
            String excelFilePath = scanner.nextLine().trim();
            if (excelFilePath.isEmpty()) {
                excelFilePath = "docs/财务元数据管理_SKU品类数据维护表_表格.xlsx";
            }
            
            // 检查Excel文件是否存在
            File excelFile = new File(excelFilePath);
            if (!excelFile.exists()) {
                log.error("Excel文件不存在: {}", excelFilePath);
                return;
            }
            
            // 获取输出文件路径
            System.out.print("请输入输出文件路径（默认：output/sku_spu_insert_statements.txt）: ");
            String outputFilePath = scanner.nextLine().trim();
            if (outputFilePath.isEmpty()) {
                outputFilePath = "output/sku_spu_insert_statements.txt";
            }
            
            // 确保输出目录存在
            File outputFile = new File(outputFilePath);
            File outputDir = outputFile.getParentFile();
            if (outputDir != null && !outputDir.exists()) {
                outputDir.mkdirs();
                log.info("创建输出目录: {}", outputDir.getAbsolutePath());
            }
            
            // 处理Excel文件
            log.info("开始处理Excel文件: {}", excelFilePath);
            excelDataProcessor.processExcelAndGenerateInsertStatements(excelFilePath, outputFilePath);
            
            log.info("处理完成！INSERT语句已生成到: {}", outputFilePath);
            
            // 显示文件信息
            if (outputFile.exists()) {
                log.info("输出文件大小: {} bytes", outputFile.length());
            }
            
        } catch (Exception e) {
            log.error("处理Excel文件时发生错误", e);
        } finally {
            scanner.close();
        }
        
        log.info("=== 程序结束 ===");
    }
} 