package com.spes.sop.tools;

import java.util.regex.Pattern;

/**
 * 规格解析测试运行器
 * 独立运行，不依赖任何框架
 *
 * <AUTHOR>
 * @since 2024-12-28
 */
public class SpecificationTestRunner {

    /**
     * 数字模式正则表达式
     */
    private static final Pattern NUMBER_PATTERN = Pattern.compile("^\\d+(\\.\\d+)?$");
    
    /**
     * 数字+ml模式正则表达式  
     */
    private static final Pattern NUMBER_ML_PATTERN = Pattern.compile("^\\d+(\\.\\d+)?ml$");

    public static void main(String[] args) {
        System.out.println("=== Excel转SQL工具 - 规格解析测试 ===");
        
        // 测试用例
        String[] testCases = {
            "100",      // 纯数字
            "50.5",     // 小数
            "200ml",    // 数字+ml
            "300ML",    // 数字+ML（大写）
            "150.5ml",  // 小数+ml
            "大瓶",     // 中文
            "500g",     // 数字+g
            "中号",     // 中文
            "",         // 空字符串
            null,       // null值
            "0",        // 零
            "1000",     // 大数字
            "25.75ml",  // 小数+ml
            "abc",      // 字母
            "100mg",    // 数字+mg
            "2.5L"      // 数字+L
        };
        
        System.out.println("\n测试用例及结果：");
        System.out.println("输入值\t\t-> 解析结果");
        System.out.println("---------------------------");
        
        for (String testCase : testCases) {
            Integer result = parseSpecification(testCase);
            System.out.printf("%-10s\t-> %d%n", 
                testCase == null ? "null" : "'" + testCase + "'", result);
        }
        
        System.out.println("\n=== 解析规则说明 ===");
        System.out.println("1. 纯数字（如：100）→ 直接使用数字");
        System.out.println("2. 数字+ml（如：200ml）→ 去除ml后使用数字");
        System.out.println("3. 其他格式 → 使用0");
        
        System.out.println("\n=== 验证结果 ===");
        validateResults();
        
        System.out.println("\n测试完成！");
    }

    /**
     * 解析规格字段
     * 如果是数字或者去除后缀（ml）是数字就使用数字，其余使用0
     *
     * @param specValue 规格值
     * @return 解析后的规格数值
     */
    private static Integer parseSpecification(String specValue) {
        if (specValue == null || specValue.trim().isEmpty()) {
            return 0;
        }
        
        String trimmedValue = specValue.trim();
        
        // 直接是数字
        if (NUMBER_PATTERN.matcher(trimmedValue).matches()) {
            try {
                return Integer.valueOf(Double.valueOf(trimmedValue).intValue());
            } catch (NumberFormatException e) {
                return 0;
            }
        }
        
        // 数字+ml格式（忽略大小写）
        if (NUMBER_ML_PATTERN.matcher(trimmedValue.toLowerCase()).matches()) {
            try {
                String numberPart = trimmedValue.toLowerCase().replace("ml", "");
                return Integer.valueOf(Double.valueOf(numberPart).intValue());
            } catch (NumberFormatException e) {
                return 0;
            }
        }
        
        // 其他情况返回0
        return 0;
    }

    /**
     * 验证关键测试用例的结果
     */
    private static void validateResults() {
        // 验证关键测试用例
        assert parseSpecification("100").equals(100) : "纯数字解析失败";
        assert parseSpecification("200ml").equals(200) : "数字+ml解析失败";
        assert parseSpecification("300ML").equals(300) : "数字+ML解析失败";
        assert parseSpecification("150.5ml").equals(150) : "小数+ml解析失败";
        assert parseSpecification("大瓶").equals(0) : "中文解析失败";
        assert parseSpecification("500g").equals(0) : "数字+g解析失败";
        assert parseSpecification("").equals(0) : "空字符串解析失败";
        assert parseSpecification(null).equals(0) : "null解析失败";
        
        System.out.println("✓ 所有关键测试用例验证通过！");
    }
} 