package com.spes.sop.common.util;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Component;

import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;

/**
 * Excel数据处理工具类
 * 用于读取Excel文件并生成SKU和SPU的INSERT语句
 *
 * <AUTHOR>
 * @since 2024-12-28
 */
@Component
@Slf4j
public class ExcelDataProcessor {

    /**
     * 数字模式正则表达式
     */
    private static final Pattern NUMBER_PATTERN = Pattern.compile("^\\d+(\\.\\d+)?$");
    
    /**
     * 数字+ml模式正则表达式  
     */
    private static final Pattern NUMBER_ML_PATTERN = Pattern.compile("^\\d+(\\.\\d+)?ml$");

    /**
     * 处理Excel文件并生成INSERT语句
     *
     * @param excelFilePath Excel文件路径
     * @param outputFilePath 输出文件路径
     */
    public void processExcelAndGenerateInsertStatements(String excelFilePath, String outputFilePath) {
        try (FileInputStream fis = new FileInputStream(excelFilePath);
             XSSFWorkbook workbook = new XSSFWorkbook(fis);
             FileWriter writer = new FileWriter(outputFilePath)) {

            Sheet sheet = workbook.getSheetAt(0);
            List<RowData> rowDataList = new ArrayList<>();

            // 读取数据（跳过标题行）
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) {
                    continue;
                }

                RowData rowData = parseRowData(row);
                if (rowData != null) {
                    rowDataList.add(rowData);
                }
            }

            // 生成INSERT语句
            generateInsertStatements(rowDataList, writer);
            
            log.info("成功处理Excel文件，生成了{}条数据的INSERT语句", rowDataList.size());

        } catch (IOException e) {
            log.error("处理Excel文件时发生错误", e);
            throw new RuntimeException("处理Excel文件失败", e);
        }
    }

    /**
     * 解析行数据
     *
     * @param row Excel行
     * @return 解析后的行数据
     */
    private RowData parseRowData(Row row) {
        try {
            RowData rowData = new RowData();
            
            // A列：SKU编码
            rowData.setSkuCode(getCellValueAsString(row.getCell(0)));
            
            // B列：SKU名称
            rowData.setSkuName(getCellValueAsString(row.getCell(1)));
            
            // C列：条码
            rowData.setBarCode(getCellValueAsString(row.getCell(2)));
            
            // D列：备案号
            rowData.setFilingNumber(getCellValueAsString(row.getCell(3)));
            
            // E列：SPU名称
            rowData.setSpuName(getCellValueAsString(row.getCell(4)));
            
            // F列：描述
            rowData.setDescription(getCellValueAsString(row.getCell(5)));
            
            // G列：品牌ID
            rowData.setBrandId(getCellValueAsLong(row.getCell(6)));
            
            // H列：一级分类
            rowData.setFirstClassification(getCellValueAsLong(row.getCell(7)));
            
            // I列：二级分类
            rowData.setSecondClassification(getCellValueAsLong(row.getCell(8)));
            
            // J列：三级分类
            rowData.setThirdClassification(getCellValueAsLong(row.getCell(9)));
            
            // K列：规格（需要特殊处理）
            rowData.setSpecification(parseSpecification(getCellValueAsString(row.getCell(10))));
            
            // L列：渠道ID
            rowData.setChannelId(getCellValueAsLong(row.getCell(11)));
            
            // M列：分类ID
            rowData.setCategoryId(getCellValueAsLong(row.getCell(12)));
            
            // N列：公允价值
            rowData.setFairPrice(getCellValueAsBigDecimal(row.getCell(13)));
            
            // O列：底价
            rowData.setBasePrice(getCellValueAsBigDecimal(row.getCell(14)));
            
            // P列：成本
            rowData.setCost(getCellValueAsBigDecimal(row.getCell(15)));

            return rowData;
        } catch (Exception e) {
            log.warn("解析行数据时发生错误，跳过该行: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 解析规格字段
     * 如果是数字或者去除后缀（ml）是数字就使用数字，其余使用0
     *
     * @param specValue 规格值
     * @return 解析后的规格数值
     */
    private Integer parseSpecification(String specValue) {
        if (specValue == null || specValue.trim().isEmpty()) {
            return 0;
        }
        
        String trimmedValue = specValue.trim();
        
        // 直接是数字
        if (NUMBER_PATTERN.matcher(trimmedValue).matches()) {
            try {
                return Integer.valueOf(Double.valueOf(trimmedValue).intValue());
            } catch (NumberFormatException e) {
                return 0;
            }
        }
        
        // 数字+ml格式
        if (NUMBER_ML_PATTERN.matcher(trimmedValue.toLowerCase()).matches()) {
            try {
                String numberPart = trimmedValue.toLowerCase().replace("ml", "");
                return Integer.valueOf(Double.valueOf(numberPart).intValue());
            } catch (NumberFormatException e) {
                return 0;
            }
        }
        
        // 其他情况返回0
        return 0;
    }

    /**
     * 生成INSERT语句
     *
     * @param rowDataList 行数据列表
     * @param writer 文件写入器
     * @throws IOException IO异常
     */
    private void generateInsertStatements(List<RowData> rowDataList, FileWriter writer) throws IOException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String currentTime = sdf.format(new Date());
        
        // 用于去重的SPU集合和SPU编码映射
        Set<String> spuSet = new HashSet<>();
        Map<String, String> spuCodeMap = new HashMap<>();
        List<String> spuInserts = new ArrayList<>();
        List<String> skuInserts = new ArrayList<>();
        
        writer.write("-- SKU和SPU批量导入SQL语句\n");
        writer.write("-- 生成时间: " + currentTime + "\n\n");
        
        for (RowData rowData : rowDataList) {
            // 生成SPU唯一键
            String spuKey = generateSpuKey(rowData);
            String spuCode;
            
            // 检查SPU是否已存在
            if (!spuSet.contains(spuKey)) {
                // 新的SPU，生成编码并添加到集合
                spuCode = CodeGenerationUtil.generateSpuCode();
                spuSet.add(spuKey);
                spuCodeMap.put(spuKey, spuCode);
                
                String spuInsert = generateSpuInsert(rowData, spuCode, currentTime);
                spuInserts.add(spuInsert);
            } else {
                // 已存在的SPU，使用之前生成的编码
                spuCode = spuCodeMap.get(spuKey);
            }
            
            // 生成SKU INSERT语句
            String skuInsert = generateSkuInsert(rowData, spuCode, currentTime);
            skuInserts.add(skuInsert);
        }
        
        // 写入SPU INSERT语句
        writer.write("-- SPU数据插入语句\n");
        for (String spuInsert : spuInserts) {
            writer.write(spuInsert + "\n");
        }
        
        writer.write("\n-- SKU数据插入语句\n");
        // 写入SKU INSERT语句
        for (String skuInsert : skuInserts) {
            writer.write(skuInsert + "\n");
        }
    }

    /**
     * 生成SPU的唯一键（用于去重）
     */
    private String generateSpuKey(RowData rowData) {
        return String.format("%s_%s_%s_%s_%s_%s_%s",
                rowData.getSpuName(),
                rowData.getDescription(),
                rowData.getBrandId(),
                rowData.getFirstClassification(),
                rowData.getSecondClassification(),
                rowData.getThirdClassification(),
                rowData.getChannelId());
    }

    /**
     * 生成SPU INSERT语句
     */
    private String generateSpuInsert(RowData rowData, String spuCode, String currentTime) {
        return String.format(
            "INSERT INTO spu (spu_code, spu_name, description, brand_id, first_classification, " +
            "second_classification, third_classification, channel_id, category_id, " +
            "creator_id, updater_id, create_time, update_time, deleted) VALUES " +
            "('%s', '%s', '%s', %s, %s, %s, %s, %s, %s, 1, 1, '%s', '%s', 0);",
            spuCode,
            escapeString(rowData.getSpuName()),
            escapeString(rowData.getDescription()),
            rowData.getBrandId(),
            rowData.getFirstClassification(),
            rowData.getSecondClassification(),
            rowData.getThirdClassification(),
            rowData.getChannelId(),
            rowData.getCategoryId(),
            currentTime,
            currentTime
        );
    }

    /**
     * 生成SKU INSERT语句
     */
    private String generateSkuInsert(RowData rowData, String spuCode, String currentTime) {
        return String.format(
            "INSERT INTO sku (spu_id, sku_code, sku_name, bar_code, filing_number, " +
            "status, specification, gift, card, fair_price, base_price, cost, " +
            "create_time, update_time, creator_id, updater_id, deleted) VALUES " +
            "((SELECT id FROM spu WHERE spu_code = '%s'), '%s', '%s', '%s', '%s', " +
            "'ACTIVE', %s, 0, 0, %s, %s, %s, '%s', '%s', 1, 1, 0);",
            spuCode,
            escapeString(rowData.getSkuCode()),
            escapeString(rowData.getSkuName()),
            escapeString(rowData.getBarCode()),
            escapeString(rowData.getFilingNumber()),
            rowData.getSpecification(),
            rowData.getFairPrice(),
            rowData.getBasePrice(),
            rowData.getCost(),
            currentTime,
            currentTime
        );
    }

    /**
     * 转义字符串中的单引号
     */
    private String escapeString(String str) {
        if (str == null) {
            return "";
        }
        return str.replace("'", "''");
    }

    /**
     * 获取单元格值作为字符串
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return new SimpleDateFormat("yyyy-MM-dd").format(cell.getDateCellValue());
                } else {
                    return String.valueOf((long) cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    /**
     * 获取单元格值作为Long
     */
    private Long getCellValueAsLong(Cell cell) {
        if (cell == null) {
            return null;
        }
        
        switch (cell.getCellType()) {
            case NUMERIC:
                return (long) cell.getNumericCellValue();
            case STRING:
                try {
                    return Long.valueOf(cell.getStringCellValue().trim());
                } catch (NumberFormatException e) {
                    return null;
                }
            default:
                return null;
        }
    }

    /**
     * 获取单元格值作为BigDecimal
     */
    private BigDecimal getCellValueAsBigDecimal(Cell cell) {
        if (cell == null) {
            return BigDecimal.ZERO;
        }
        
        switch (cell.getCellType()) {
            case NUMERIC:
                return BigDecimal.valueOf(cell.getNumericCellValue());
            case STRING:
                try {
                    return new BigDecimal(cell.getStringCellValue().trim());
                } catch (NumberFormatException e) {
                    return BigDecimal.ZERO;
                }
            default:
                return BigDecimal.ZERO;
        }
    }

    /**
     * 行数据模型
     */
    @Data
    public static class RowData {
        // SKU信息
        private String skuCode;
        private String skuName;
        private String barCode;
        private String filingNumber;
        private Integer specification;
        private BigDecimal fairPrice;
        private BigDecimal basePrice;
        private BigDecimal cost;
        
        // SPU信息
        private String spuName;
        private String description;
        private Long brandId;
        private Long firstClassification;
        private Long secondClassification;
        private Long thirdClassification;
        private Long channelId;
        private Long categoryId;
    }
} 