/*
 * Copyright(C) 2024 Spes Co., Ltd. All rights reserved.
 */

package com.spes.sop.third.kingdee.config;

import cn.hutool.core.util.StrUtil;
import com.spes.sop.third.kingdee.client.K3CloudTemplate;
import com.spes.sop.third.kingdee.model.base.Authentication;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 金蝶账套管理服务
 * 负责管理多品牌对应的不同账套
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class KingdeeAccountUtil {

    private final KingdeeConfig kingdeeConfig;

    private static KingdeeAccountUtil instance;

    /**
     * 缓存不同账套的K3CloudTemplate实例
     * Key: 账套ID, Value: K3CloudTemplate实例
     */
    private static final ConcurrentHashMap<String, K3CloudTemplate> templateCache = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        instance = this;
        log.info("KingdeeAccountUtil initialized with config: serverUrl={}",
                kingdeeConfig.getServerUrl());
    }

    /**
     * 根据品牌名称获取对应的K3CloudTemplate
     *
     * @param brandName 品牌名称
     * @return K3CloudTemplate实例
     */
    public static K3CloudTemplate getTemplateByBrand(String brandName) {
        String acctId = getAcctIdByBrand(brandName);
        return getTemplateByAcctId(acctId);
    }

    /**
     * 根据账套ID获取对应的K3CloudTemplate
     *
     * @param acctId 账套ID
     * @return K3CloudTemplate实例
     */
    public static K3CloudTemplate getTemplateByAcctId(String acctId) {
        if (StrUtil.isBlank(acctId)) {
            acctId = getAcctIdByBrand("默认");
        }
        return templateCache.computeIfAbsent(acctId, KingdeeAccountUtil::createTemplate);
    }

    /**
     * 根据品牌名称获取对应的账套ID
     *
     * @param brandName 品牌名称
     * @return 账套ID
     */
    public static String getAcctIdByBrand(String brandName) {
        if (instance == null || instance.kingdeeConfig == null) {
            log.warn("KingdeeConfig not initialized, using default acctId");
            return "66d67292cd0454"; // 默认账套ID
        }
        return instance.kingdeeConfig.getAcctIdByBrand(brandName);
    }

    /**
     * 创建K3CloudTemplate实例
     *
     * @param acctId 账套ID
     * @return K3CloudTemplate实例
     */
    private static K3CloudTemplate createTemplate(String acctId) {
        if (instance == null || instance.kingdeeConfig == null) {
            log.error("KingdeeConfig not initialized, cannot create template");
            throw new IllegalStateException("KingdeeConfig not properly initialized");
        }

        KingdeeConfig config = instance.kingdeeConfig;
        log.info("Creating K3CloudTemplate for acctId: {}, serverUrl: {}", acctId, config.getServerUrl());

        // 创建认证信息
        Authentication auth = new Authentication(
                acctId,
                config.getUsername(),
                config.getPassword(),
                config.getLcid()
        );

        return new K3CloudTemplate(config.getServerUrl(), auth);
    }

    /**
     * 清除指定账套的缓存
     *
     * @param acctId 账套ID
     */
    public static void clearTemplateCache(String acctId) {
        if (StrUtil.isNotBlank(acctId)) {
            templateCache.remove(acctId);
            log.info("Cleared template cache for acctId: {}", acctId);
        }
    }

    /**
     * 清除所有缓存
     */
    public static void clearAllTemplateCache() {
        templateCache.clear();
        log.info("Cleared all template cache");
    }

    /**
     * 获取当前缓存的账套数量
     *
     * @return 缓存的账套数量
     */
    public static int getCacheSize() {
        return templateCache.size();
    }
} 