package com.spes.sop.common.util;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import javax.annotation.Resource;
import java.io.File;

/**
 * Excel数据处理工具类测试
 *
 * <AUTHOR>
 * @since 2024-12-28
 */
@SpringBootTest
@ContextConfiguration(classes = {ExcelDataProcessor.class})
public class ExcelDataProcessorTest {

    @Resource
    private ExcelDataProcessor excelDataProcessor;

    /**
     * 测试Excel文件处理功能
     */
    @Test
    public void testProcessExcelAndGenerateInsertStatements() {
        // 测试文件路径
        String excelFilePath = "docs/财务元数据管理_SKU品类数据维护表_表格.xlsx";
        String outputFilePath = "output/sku_spu_insert_statements.txt";
        
        // 确保输出目录存在
        File outputDir = new File("output");
        if (!outputDir.exists()) {
            outputDir.mkdirs();
        }
        
        try {
            // 处理Excel文件
            excelDataProcessor.processExcelAndGenerateInsertStatements(excelFilePath, outputFilePath);
            
            System.out.println("Excel文件处理完成，INSERT语句已生成到: " + outputFilePath);
            
            // 验证输出文件是否存在
            File outputFile = new File(outputFilePath);
            if (outputFile.exists()) {
                System.out.println("输出文件大小: " + outputFile.length() + " bytes");
            } else {
                System.err.println("输出文件不存在！");
            }
            
        } catch (Exception e) {
            System.err.println("处理Excel文件时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试规格解析功能
     */
    @Test
    public void testSpecificationParsing() {
        ExcelDataProcessor processor = new ExcelDataProcessor();
        
        // 测试不同格式的规格值
        System.out.println("测试规格解析功能:");
        System.out.println("'100' -> " + parseSpecificationTest(processor, "100"));
        System.out.println("'50.5' -> " + parseSpecificationTest(processor, "50.5"));
        System.out.println("'200ml' -> " + parseSpecificationTest(processor, "200ml"));
        System.out.println("'300ML' -> " + parseSpecificationTest(processor, "300ML"));
        System.out.println("'150.5ml' -> " + parseSpecificationTest(processor, "150.5ml"));
        System.out.println("'大瓶' -> " + parseSpecificationTest(processor, "大瓶"));
        System.out.println("'500g' -> " + parseSpecificationTest(processor, "500g"));
        System.out.println("'' -> " + parseSpecificationTest(processor, ""));
        System.out.println("null -> " + parseSpecificationTest(processor, null));
    }
    
    /**
     * 通过反射调用私有方法进行测试
     */
    private Integer parseSpecificationTest(ExcelDataProcessor processor, String specValue) {
        try {
            java.lang.reflect.Method method = ExcelDataProcessor.class.getDeclaredMethod("parseSpecification", String.class);
            method.setAccessible(true);
            return (Integer) method.invoke(processor, specValue);
        } catch (Exception e) {
            e.printStackTrace();
            return -1;
        }
    }
} 