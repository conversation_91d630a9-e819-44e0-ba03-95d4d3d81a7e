package com.spes.sop.common.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;

import java.io.File;
import java.lang.reflect.Method;

/**
 * Excel数据处理工具类简单测试
 * 不依赖Spring Boot上下文的独立测试
 *
 * <AUTHOR>
 * @since 2024-12-28
 */
public class ExcelDataProcessorSimpleTest {

    private ExcelDataProcessor excelDataProcessor;

    @BeforeEach
    public void setUp() {
        excelDataProcessor = new ExcelDataProcessor();
    }

    /**
     * 测试规格解析功能
     */
    @Test
    public void testSpecificationParsing() {
        System.out.println("=== 测试规格解析功能 ===");
        
        // 测试不同格式的规格值
        String[] testCases = {
            "100",      // 纯数字
            "50.5",     // 小数
            "200ml",    // 数字+ml
            "300ML",    // 数字+ML（大写）
            "150.5ml",  // 小数+ml
            "大瓶",     // 中文
            "500g",     // 数字+g
            "中号",     // 中文
            "",         // 空字符串
            null,       // null值
            "0",        // 零
            "1000",     // 大数字
            "25.75ml",  // 小数+ml
            "abc",      // 字母
            "100mg",    // 数字+mg
            "2.5L"      // 数字+L
        };
        
        System.out.println("输入值\t\t-> 解析结果");
        System.out.println("---------------------------");
        
        for (String testCase : testCases) {
            Integer result = parseSpecificationTest(testCase);
            System.out.printf("%-10s\t-> %d%n", 
                testCase == null ? "null" : "'" + testCase + "'", result);
        }
        
        System.out.println("\n=== 解析规则说明 ===");
        System.out.println("1. 纯数字（如：100）→ 直接使用数字");
        System.out.println("2. 数字+ml（如：200ml）→ 去除ml后使用数字");
        System.out.println("3. 其他格式 → 使用0");
    }

    /**
     * 测试Excel文件处理功能（如果文件存在）
     */
    @Test
    public void testProcessExcelIfFileExists() {
        // 测试文件路径
        String excelFilePath = "docs/财务元数据管理_SKU品类数据维护表_表格.xlsx";
        String outputFilePath = "output/sku_spu_insert_statements_test.txt";
        
        File excelFile = new File(excelFilePath);
        if (!excelFile.exists()) {
            System.out.println("Excel文件不存在，跳过测试: " + excelFilePath);
            return;
        }
        
        // 确保输出目录存在
        File outputDir = new File("output");
        if (!outputDir.exists()) {
            outputDir.mkdirs();
        }
        
        try {
            System.out.println("开始处理Excel文件: " + excelFilePath);
            
            // 处理Excel文件
            excelDataProcessor.processExcelAndGenerateInsertStatements(excelFilePath, outputFilePath);
            
            System.out.println("Excel文件处理完成，INSERT语句已生成到: " + outputFilePath);
            
            // 验证输出文件是否存在
            File outputFile = new File(outputFilePath);
            if (outputFile.exists()) {
                System.out.println("输出文件大小: " + outputFile.length() + " bytes");
                System.out.println("测试成功！");
            } else {
                System.err.println("输出文件不存在！");
            }
            
        } catch (Exception e) {
            System.err.println("处理Excel文件时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试单元格值解析功能
     */
    @Test
    public void testCellValueParsing() {
        System.out.println("=== 测试单元格值解析功能 ===");
        
        // 这里可以添加更多的单元测试
        // 由于Apache POI的Cell对象需要实际的Excel文件，这里主要测试规格解析
        testSpecificationParsing();
    }
    
    /**
     * 通过反射调用私有方法进行测试
     */
    private Integer parseSpecificationTest(String specValue) {
        try {
            Method method = ExcelDataProcessor.class.getDeclaredMethod("parseSpecification", String.class);
            method.setAccessible(true);
            return (Integer) method.invoke(excelDataProcessor, specValue);
        } catch (Exception e) {
            System.err.println("反射调用失败: " + e.getMessage());
            return -1;
        }
    }
} 