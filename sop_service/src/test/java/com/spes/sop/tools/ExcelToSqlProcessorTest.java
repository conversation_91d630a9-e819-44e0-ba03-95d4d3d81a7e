package com.spes.sop.tools;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.io.File;

/**
 * Excel转SQL处理器测试类
 *
 * <AUTHOR>
 */
@SpringBootTest
@SpringJUnitConfig
public class ExcelToSqlProcessorTest {

    @Test
    public void testProcessExcelToSql() {
        ExcelToSqlProcessor processor = new ExcelToSqlProcessor();
        
        // 测试文件路径
        String excelFilePath = "docs/示例数据.xlsx"; // 需要手动创建Excel文件
        String outputFilePath = "output/test_output.txt";
        
        // 检查输入文件是否存在
        File inputFile = new File(excelFilePath);
        if (!inputFile.exists()) {
            System.out.println("测试跳过：Excel文件不存在 - " + excelFilePath);
            System.out.println("请根据docs/示例数据.csv创建对应的Excel文件进行测试");
            return;
        }
        
        try {
            // 执行处理
            processor.processExcelToSql(excelFilePath, outputFilePath);
            
            // 检查输出文件是否生成
            File outputFile = new File(outputFilePath);
            if (outputFile.exists()) {
                System.out.println("测试成功：SQL文件已生成 - " + outputFilePath);
                System.out.println("文件大小：" + outputFile.length() + " 字节");
            } else {
                System.out.println("测试失败：SQL文件未生成");
            }
            
        } catch (Exception e) {
            System.err.println("测试失败：" + e.getMessage());
            e.printStackTrace();
        }
    }
    
    @Test
    public void testSpecificationProcessing() {
        ExcelToSqlProcessor processor = new ExcelToSqlProcessor();
        
        // 测试规格处理逻辑
        System.out.println("=== 规格处理测试 ===");
        
        // 使用反射调用私有方法进行测试
        try {
            java.lang.reflect.Method method = ExcelToSqlProcessor.class.getDeclaredMethod("processSpecification", String.class);
            method.setAccessible(true);
            
            // 测试用例
            String[] testCases = {
                "500ml",    // 应该返回 500
                "1000",     // 应该返回 1000
                "250g",     // 应该返回 250
                "1.5L",     // 应该返回 1
                "大号",      // 应该返回 0
                "",         // 应该返回 0
                null,       // 应该返回 0
                "abc123",   // 应该返回 0
                "123abc456" // 应该返回 123
            };
            
            for (String testCase : testCases) {
                Integer result = (Integer) method.invoke(processor, testCase);
                System.out.println(String.format("输入: %-10s -> 输出: %d", 
                    testCase == null ? "null" : "'" + testCase + "'", result));
            }
            
        } catch (Exception e) {
            System.err.println("规格处理测试失败：" + e.getMessage());
            e.printStackTrace();
        }
    }
}
