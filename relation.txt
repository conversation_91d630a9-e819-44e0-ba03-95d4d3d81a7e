# 属性关联关系配置文件
# 格式：属性名称=属性ID
# 
# 说明：
# 1. 品牌、渠道、系列、分类使用单个ID
# 2. 四级分类使用逗号分隔的四个ID，格式：一级ID,二级ID,三级ID,四级ID
# 3. 以#开头的行为注释，会被忽略

# ==================== 品牌关联 ====================
品牌A=1
品牌B=2
品牌C=3
可口可乐=4
百事可乐=5
农夫山泉=6
康师傅=7
统一=8

# ==================== 渠道关联 ====================
渠道1=1
渠道2=2
渠道3=3
线上渠道=4
线下渠道=5
超市渠道=6
便利店渠道=7
电商渠道=8

# ==================== 系列关联 ====================
系列A=1
系列B=2
系列C=3
经典系列=4
高端系列=5
健康系列=6
儿童系列=7

# ==================== 分类关联 ====================
分类A=1
分类B=2
分类C=3
饮料分类=4
食品分类=5
日用品分类=6

# ==================== 四级分类关联 ====================
# 格式：完整分类路径=一级ID,二级ID,三级ID,四级ID
食品>饮料>果汁>苹果汁=1,2,3,4
食品>饮料>果汁>橙汁=1,2,3,5
食品>饮料>碳酸饮料>可乐=1,2,4,6
食品>饮料>碳酸饮料>雪碧=1,2,4,7
食品>饮料>茶饮料>绿茶=1,2,5,8
食品>饮料>茶饮料>红茶=1,2,5,9
食品>零食>薯片>原味=1,6,7,10
食品>零食>薯片>烧烤味=1,6,7,11
日用品>洗护>洗发水>去屑=2,8,9,12
日用品>洗护>洗发水>滋养=2,8,9,13
